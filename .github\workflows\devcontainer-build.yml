name: Devcontainer Pre-Build

on:
  workflow_dispatch:
  schedule:
    - cron: "0 3 * * 0"
  # push:
  #   branches:
  #     - "master"
  #   tags:
  #     - "v*.*.*"
  #   pull_requests:
  #     branches:
  #       - "master"

concurrency:
  group: "${{ github.workflow }}"
  cancel-in-progress: true


jobs:
  build-and-push:
    permissions:
      packages: write
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        persist-credentials: false
    - name: Login to GitHub Container Registry
      uses: docker/login-action@74a5d142397b4f367a81961eba4e8cd7edddf772 # v3.4.0
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    - name: Pre-build dev container image
      uses: devcontainers/ci@8bf61b26e9c3a98f69cb6ce2f88d24ff59b785c6 # v0.3.19
      with:
        subFolder: .github
        imageName: ghcr.io/${{ github.repository }}-devcontainer
        cacheFrom: ghcr.io/${{ github.repository }}-devcontainer
        push: always
