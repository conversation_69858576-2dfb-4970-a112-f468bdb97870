name: GitHub Actions Security Analysis with zizmor 🌈

on:
  push:
    branches:
      - develop
      - stable
  pull_request:
    branches:
      - develop
      - stable

permissions: {}

jobs:
  zizmor:
    runs-on: ubuntu-latest
    permissions:
      security-events: write
      # contents: read # only needed for private repos
      # actions: read # only needed for private repos
    steps:
      - name: Checkout repository
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          persist-credentials: false

      - name: Run zizmor 🌈
        uses: zizmorcore/zizmor-action@f52a838cfabf134edcbaa7c8b3677dde20045018 # v0.1.1
