name: Docker Build and Deploy

on:
  workflow_call:
    secrets:
      DOCKER_PASSWORD:
        required: true
      DOCKER_USERNAME:
        required: true
      DISCORD_WEBHOOK:
        required: false
  workflow_dispatch:
    inputs:
      branch_name:
        description: 'Branch name to build Docker images for'
        required: false
        default: 'develop'
        type: string

permissions:
  contents: read

jobs:
  deploy-docker:
    runs-on: ubuntu-22.04
    if: github.repository == 'freqtrade/freqtrade'

    steps:
    - uses: actions/checkout@v4
      with:
        persist-credentials: false

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: "3.12"

    - name: Extract branch name
      id: extract-branch
      env:
        BRANCH_NAME_INPUT: ${{ github.event.inputs.branch_name }}
      run: |
        if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
          BRANCH_NAME="${BRANCH_NAME_INPUT}"
        else
          BRANCH_NAME="${GITHUB_REF##*/}"
        fi
        echo "GITHUB_REF='${GITHUB_REF}'"
        echo "branch=${BRANCH_NAME}" >> "$GITHUB_OUTPUT"

    - name: Dockerhub login
      env:
        DOCKER_PASSWORD: ${{ secrets.DOCKER_PASSWORD }}
        DOCKER_USERNAME: ${{ secrets.DOCKER_USERNAME }}
      run: |
        echo "${DOCKER_PASSWORD}" | docker login --username ${DOCKER_USERNAME} --password-stdin

    - name: Set up QEMU
      uses: docker/setup-qemu-action@29109295f81e9208d7d86ff1c6c12d2833863392 # v3.6.0

    - name: Set up Docker Buildx
      id: buildx
      uses: docker/setup-buildx-action@e468171a9de216ec08956ac3ada2f0791b6bd435 #v3.11.1

    - name: Available platforms
      run: echo ${PLATFORMS}
      env:
        PLATFORMS: ${{ steps.buildx.outputs.platforms }}

    - name: Build and test and push docker images
      env:
        BRANCH_NAME: ${{ steps.extract-branch.outputs.branch }}
      run: |
        build_helpers/publish_docker_multi.sh

  deploy-arm:
    name: "Deploy Docker"
    permissions:
      packages: write
    needs: [ deploy-docker ]
    # Only run on 64bit machines
    runs-on: [self-hosted, linux, ARM64]
    if: github.repository == 'freqtrade/freqtrade'

    steps:
    - uses: actions/checkout@v4
      with:
        persist-credentials: false

    - name: Extract branch name
      id: extract-branch
      env:
        BRANCH_NAME_INPUT: ${{ github.event.inputs.branch_name }}
      run: |
        if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
          BRANCH_NAME="${BRANCH_NAME_INPUT}"
        else
          BRANCH_NAME="${GITHUB_REF##*/}"
        fi
        echo "GITHUB_REF='${GITHUB_REF}'"
        echo "branch=${BRANCH_NAME}" >> "$GITHUB_OUTPUT"

    - name: Dockerhub login
      env:
        DOCKER_PASSWORD: ${{ secrets.DOCKER_PASSWORD }}
        DOCKER_USERNAME: ${{ secrets.DOCKER_USERNAME }}
      run: |
        echo "${DOCKER_PASSWORD}" | docker login --username ${DOCKER_USERNAME} --password-stdin

    - name: Build and test and push docker images
      env:
        BRANCH_NAME: ${{ steps.extract-branch.outputs.branch }}
        GHCR_USERNAME: ${{ github.actor }}
        GHCR_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      run: |
        build_helpers/publish_docker_arm64.sh

    - name: Discord notification
      uses: rjstone/discord-webhook-notify@c2597273488aeda841dd1e891321952b51f7996f #v2.2.1
      if: always() && ( github.event_name != 'pull_request' || github.event.pull_request.head.repo.fork == false) && (github.event_name != 'schedule')
      with:
          severity: info
          details: Deploy Succeeded!
          webhookUrl: ${{ secrets.DISCORD_WEBHOOK }}
