## Highlighted changes

- ...

### How to update

As always, you can update your bot using one of the following commands:

#### docker-compose

```bash
docker-compose pull
docker-compose up -d
```

#### Installation via setup script

```
# Deactivate venv and run 
./setup.sh --update
```

#### Plain native installation

```
git pull
pip install -U -r requirements.txt
```

<details>
<summary>Expand full changelog</summary>

```
<Paste your changelog here>
```

</details>
