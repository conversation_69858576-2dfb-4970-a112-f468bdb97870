FROM freqtradeorg/freqtrade:develop_freqairl

USER root
# Install dependencies
COPY requirements-dev.txt /freqtrade/

ARG USERNAME=ftuser

RUN apt-get update \
    && apt-get -y install --no-install-recommends apt-utils dialog git ssh vim build-essential zsh \
    && apt-get clean \
    && mkdir -p /home/<USER>/.vscode-server /home/<USER>/.vscode-server-insiders /home/<USER>/commandhistory \
    && chown ${USERNAME}:${USERNAME} -R /home/<USER>/.local/ \
    && chown ${USERNAME}: -R /home/<USER>/

USER ftuser

RUN pip install --user autopep8 -r docs/requirements-docs.txt -r requirements-dev.txt --no-cache-dir

# Empty the ENTRYPOINT to allow all commands
ENTRYPOINT []
