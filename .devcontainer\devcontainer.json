{
    "name": "freqtrade Develop",
    "image": "ghcr.io/freqtrade/freqtrade-devcontainer:latest",
    // Use 'forwardPorts' to make a list of ports inside the container available locally.
    "forwardPorts": [
        8080
    ],
    "workspaceMount": "source=${localWorkspaceFolder},target=/workspaces/freqtrade,type=bind,consistency=cached",
    // Uncomment to connect as a non-root user if you've added one. See https://aka.ms/vscode-remote/containers/non-root.
    "remoteUser": "ftuser",
    "onCreateCommand": "pip install --user -e .",
    "postCreateCommand": "freqtrade create-userdir --userdir user_data/",
    "workspaceFolder": "/workspaces/freqtrade",
    "customizations": {
        "vscode": {
            "settings": {
                "terminal.integrated.shell.linux": "/bin/bash",
                "editor.insertSpaces": true,
                "files.trimTrailingWhitespace": true,
                "[markdown]": {
                    "files.trimTrailingWhitespace": false
                },
                "python.pythonPath": "/usr/local/bin/python",
                "[python]": {
                    "editor.codeActionsOnSave": {
                        "source.organizeImports": "explicit"
                    },
                    "editor.formatOnSave": true,
                    "editor.defaultFormatter": "charliermarsh.ruff"
                }
            },
            // Add the IDs of extensions you want installed when the container is created.
            "extensions": [
                "ms-python.python",
                "ms-python.vscode-pylance",
                "charliermarsh.ruff",
                "davidanson.vscode-markdownlint",
                "ms-azuretools.vscode-docker",
                "vscode-icons-team.vscode-icons",
                "github.vscode-github-actions",
            ],
        }
    }
}
